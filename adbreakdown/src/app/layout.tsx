import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import { <PERSON><PERSON><PERSON>ider } from '@clerk/nextjs'
import './globals.css'
import { generateMetadata, seoConfigs, structuredData } from '@/lib/seo'
import GoogleAnalytics from '@/components/GoogleAnalytics'

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' })

export const metadata: Metadata = generateMetadata({
  ...seoConfigs.home,
  structuredData: [
    structuredData.organization,
    structuredData.website,
    structuredData.softwareApplication
  ]
})

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ClerkProvider>
      <html lang="en" className={inter.variable}>
        <head>
          <GoogleAnalytics />
        </head>
        <body className={inter.className}>
          {children}
        </body>
      </html>
    </ClerkProvider>
  )
}
