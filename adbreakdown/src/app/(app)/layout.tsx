'use client'
import React from 'react'

import { 
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { 
  Home,
  BarChart3,
  Calendar,
  Library,
  Settings,
  HelpCircle,
  User,
  CreditCard,
  LogOut,
  ChevronUp
} from "lucide-react"
import Link from "next/link"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useUser, useClerk } from "@clerk/nextjs"

// Menu items for the app sidebar
const data = {
  navMain: [
    {
      title: "",
      items: [
        {
          title: "Studio",
          url: "/studio",
          icon: Home,
          description: "Dashboard overview"
        },
        {
          title: "Optimise",
          url: "/studio/optimise",
          icon: BarChart3,
          description: "Private ad analysis"
        },
        {
          title: "Create",
          url: "/studio/create",
          icon: CreditCard,
          description: "AI-powered ad creation",
          comingSoon: true
        },
        {
          title: "Plan",
          url: "/studio/plan",
          icon: Calendar,
          description: "Campaign planning",
          comingSoon: true
        },
        {
          title: "Explore",
          url: "/studio/explore",
          icon: Library,
          description: "Discover trends",
          comingSoon: true
        },
        {
          title: "Brand",
          url: "/studio/brand",
          icon: User,
          description: "Brand management",
          comingSoon: true
        }, 
        {
          title: "Ad Library",
          url: "/ad-library",
          icon: Library,
          description: "Ad Library"
        }
      ]
    }
  ]
}

// Function to get page info based on pathname
function getPageInfo(pathname: string, brandName?: string) {
  const pathSegments = pathname.split('/').filter(Boolean)
  
  // Map pathnames to page info
  const pageMap: Record<string, { title: string; description: string; icon?: any }> = {
    '/studio': { 
      title: 'Studio', 
      description: 'Your portal to AI-powered ad analysis and creation',
      icon: Home
    },
    '/studio/optimise': { 
      title: 'Optimise', 
      description: 'Get optimization recommendations for your ad assets before launching with our advanced AI',
      icon: BarChart3
    },
    '/studio/create': { 
      title: 'Create', 
      description: 'AI-powered ad creation',
      icon: CreditCard
    },
    '/studio/plan': { 
      title: 'Plan', 
      description: 'Campaign planning',
      icon: Calendar
    },
    '/studio/explore': { 
      title: 'Explore', 
      description: 'Discover trends',
      icon: Library
    },
    '/studio/brand': { 
      title: 'Brand', 
      description: 'Brand management',
      icon: User
    },
    '/ad-library': { 
      title: 'Ad Library', 
      description: 'Browse and analyze ad creatives',
      icon: Library
    },
    '/billing': { 
      title: 'Billing', 
      description: 'Manage your subscription and billing',
      icon: CreditCard
    },
    '/settings': { 
      title: 'Settings', 
      description: 'Account and application settings',
      icon: Settings
    },
    '/help': { 
      title: 'Help', 
      description: 'Get support and documentation',
      icon: HelpCircle
    }
  }

  // Check for exact match first
  if (pageMap[pathname]) {
    return pageMap[pathname]
  }

  // Check for dynamic routes (like /studio/private/[slug])
  if (pathname.startsWith('/studio/private/')) {
    return { 
      title: 'Pre-launch Analysis', 
      description: 'Confidential analysis for pre-launch optimization',
      icon: BarChart3
    }
  }

  // Check for brand profile routes
  if (pathname.startsWith('/studio/brand/') && pathname !== '/studio/brand' && pathname !== '/studio/brand/create') {
    const segments = pathname.split('/')
    if (segments.length >= 4) {
      const isEdit = segments[4] === 'edit'
      return { 
        title: `Brand Profile › ${brandName || 'Loading...'}`, 
        description: isEdit ? 'Edit brand profile settings' : 'View and manage brand profile',
        icon: User
      }
    }
  }

  // Default fallback
  return { 
    title: pathSegments[pathSegments.length - 1]?.charAt(0).toUpperCase() + pathSegments[pathSegments.length - 1]?.slice(1) || 'Dashboard',
    description: 'Navigate your workspace',
    icon: Home
  }
}

export default function AppLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const [brandName, setBrandName] = useState<string>('')
  const { user } = useUser()
  const { signOut } = useClerk()
  
  // Fetch brand name if on brand profile page
  useEffect(() => {
    if (pathname.startsWith('/studio/brand/') && pathname !== '/studio/brand' && pathname !== '/studio/brand/create') {
      const segments = pathname.split('/')
      const brandId = segments[3]
      
      if (brandId) {
        fetch(`/api/brands/${brandId}`)
          .then(res => res.json())
          .then(data => {
            if (data.brand?.brand_name) {
              setBrandName(data.brand.brand_name)
            }
          })
          .catch(console.error)
      }
    } else {
      setBrandName('')
    }
  }, [pathname])
  
  const pageInfo = getPageInfo(pathname, brandName)
  return (
    <SidebarProvider>
      <Sidebar variant="inset" collapsible="icon">
        <SidebarHeader>
          <div className="flex items-center gap-2 px-2 py-2">
            <div className="flex h-8 w-8 items-center justify-center group-data-[collapsible=icon]:h-5 group-data-[collapsible=icon]:w-5">
              <Image
                src="/logo.png"
                alt="breakdown.ad logo"
                width={60}
                height={60}
                className="rounded-lg group-data-[collapsible=icon]:w-5 group-data-[collapsible=icon]:h-5"
              />
            </div>
            <div className="grid flex-1 text-left text-lg leading-tight group-data-[collapsible=icon]:hidden">
              <span className="truncate font-semibold">Breakdown.ad</span>
              
              <span className="truncate text-xs text-muted-foreground">
              AI for Ads
              </span>
          
            </div>
          </div>
        </SidebarHeader>
        
        <SidebarContent>
          {data.navMain.map((group) => (
            <SidebarGroup key={group.title}>
              <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
              <SidebarGroupContent>
                <SidebarMenu className="space-y-0 gap-0"> {/* Add this class */}
                  {group.items.map((item) => (
                    <SidebarMenuItem key={item.title} className="py-0 my-0"> {/* Reduce vertical padding */}
                      <SidebarMenuButton 
                        asChild={!item.comingSoon} 
                        tooltip={item.description}
                        size="lg"
                        className={item.comingSoon ? "opacity-50 cursor-not-allowed" : ""}
                      >
                        {item.comingSoon ? (
                          <div className="flex items-center justify-between w-full">
                            <div className="flex items-center gap-3">
                              <item.icon className="h-5 w-5" />
                              <span className="text-base">{item.title}</span>
                            </div>
                            <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-orange-50 text-orange-700 border-orange-200">
                              Coming Soon
                            </Badge>
                          </div>
                        ) : (
                          <Link href={item.url} className="flex items-center gap-3">
                            <item.icon className="h-5 w-5" />
                            <span className="text-base">{item.title}</span>
                          </Link>
                        )}
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          ))}
        </SidebarContent>
        
        <SidebarFooter>
          <SidebarMenu>
            <SidebarMenuItem>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <SidebarMenuButton
                    size="lg"
                    className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                  >
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage 
                        src={user?.imageUrl} 
                        alt={user?.fullName || user?.emailAddresses?.[0]?.emailAddress || 'User'} 
                      />
                      <AvatarFallback className="rounded-lg">
                        {user?.firstName?.charAt(0) || user?.emailAddresses?.[0]?.emailAddress?.charAt(0) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {user?.fullName || user?.emailAddresses?.[0]?.emailAddress || 'User'}
                      </span>
                      <span className="truncate text-xs">
                        {user?.emailAddresses?.[0]?.emailAddress}
                      </span>
                    </div>
                    <ChevronUp className="ml-auto size-4" />
                  </SidebarMenuButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                  side="top"
                  align="end"
                  sideOffset={4}
                >
                  <DropdownMenuItem asChild>
                    <Link href="/billing" className="flex items-center gap-2 cursor-pointer">
                      <CreditCard className="h-4 w-4" />
                      <span>Billing</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/settings" className="flex items-center gap-2 cursor-pointer">
                      <Settings className="h-4 w-4" />
                      <span>Settings</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/help" className="flex items-center gap-2 cursor-pointer">
                      <HelpCircle className="h-4 w-4" />
                      <span>Help</span>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => signOut()} 
                    className="flex items-center gap-2 cursor-pointer text-red-600 focus:text-red-600"
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarFooter>
        
        <SidebarRail />
      </Sidebar>
      
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <div className="flex items-center gap-3">
              <div>
                <h1 className="text-lg font-semibold text-gray-900">{pageInfo.title}</h1>
               
              {/*}  <p className="text-xs text-gray-500 leading-tight">{pageInfo.description}</p> */}
              
              </div>
            </div>
          </div>
        </header>
        
        <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
          {children}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}