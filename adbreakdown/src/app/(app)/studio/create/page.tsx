'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Bot, Sparkles, Zap, Target, TrendingUp, Clock } from 'lucide-react'

export default function CreatePage() {
  const features = [
    {
      icon: <Bot className="h-8 w-8 text-blue-600" />,
      title: 'AI-Powered Agent',
      description: 'Your personal AI assistant that understands video advertising and marketing strategies.'
    },
    {
      icon: <Sparkles className="h-8 w-8 text-purple-600" />,
      title: 'Creative Generation',
      description: 'Generate ad scripts, storyboards, and creative concepts based on successful patterns.'
    },
    {
      icon: <Target className="h-8 w-8 text-green-600" />,
      title: 'Smart Targeting',
      description: 'Get AI-recommended audience segments and targeting strategies for maximum impact.'
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-orange-600" />,
      title: 'Performance Optimization',
      description: 'Continuously learn from your campaigns and suggest improvements in real-time.'
    },
    {
      icon: <Zap className="h-8 w-8 text-yellow-600" />,
      title: 'Instant Insights',
      description: 'Get immediate feedback on your ad concepts before you invest in production.'
    },
    {
      icon: <Clock className="h-8 w-8 text-indigo-600" />,
      title: '24/7 Availability',
      description: 'Your AI agent works around the clock, ready to help whenever inspiration strikes.'
    }
  ]

  return (
    <div className="flex flex-1 flex-col gap-6">
      {/* Header */}
      <div className="flex items-center gap-4 mb-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create</h1>
          <p className="text-gray-600">AI-powered ad creation and optimization tools</p>
        </div>
      </div>

      {/* Coming Soon Badge */}
      <div className="flex justify-start mb-6">
        <Badge variant="secondary" className="bg-blue-100 text-blue-700 px-4 py-2 text-lg">
          <Sparkles className="h-4 w-4 mr-2" />
          Coming Soon
        </Badge>
      </div>

      {/* Hero Content */}
      <Card className="mb-8">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl mb-4">Meet Your AI Ad Agent</CardTitle>
          <CardDescription className="text-lg">
            The future of advertising is here. Our AI Agent will be your personal marketing assistant, 
            helping you create, optimize, and scale video ads like never before.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700" disabled>
              <Sparkles className="h-5 w-5 mr-2" />
              Join Waitlist
            </Button>
            <Button size="lg" variant="outline" onClick={() => window.location.href = '/ad-library'}>
              Explore Ad Library
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Features Grid */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">
          What to Expect from Ad Agent
        </h2>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <CardTitle className="text-lg mb-2">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-gray-600">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Timeline Section */}
      <Card className="bg-gray-50">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">Development Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="max-w-3xl mx-auto">
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-green-600 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 1: Core Analysis Engine</h3>
                  <p className="text-gray-600">✅ Completed - Advanced video ad analysis capabilities</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-blue-600 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 2: AI Agent Development</h3>
                  <p className="text-gray-600">🔄 In Progress - Building conversational AI and creative generation</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 3: Beta Testing</h3>
                  <p className="text-gray-600">⏳ Q2 2025 - Limited beta access for early adopters</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 4: Public Launch</h3>
                  <p className="text-gray-600">🚀 Q3 2025 - Full public release with all features</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}