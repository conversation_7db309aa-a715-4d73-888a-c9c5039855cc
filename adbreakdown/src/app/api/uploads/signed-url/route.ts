import { Storage } from '@google-cloud/storage';
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { v4 as uuidv4 } from 'uuid';

// Initialize Google Cloud Storage
let storage: Storage;

try {
  // Parse the GCS private key (it's stored as full service account JSON in env var)
  const gcsCredentials = JSON.parse(process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY || '{}');
  
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
    credentials: gcsCredentials, // Use the full credentials object
  });
  
  console.log('✅ GCS Storage initialized successfully');
} catch (error) {
  console.error('❌ Failed to initialize GCS storage:', error);
  // Fallback initialization without credentials (will fail at runtime but allows server to start)
  storage = new Storage({
    projectId: process.env.GOOGLE_PROJECT_ID,
  });
}

const bucketName = process.env.NEXT_PUBLIC_GCS_BUCKET_NAME;

export async function POST(req: NextRequest) {
  // Authentication check
  const { userId } = await auth();
  if (!userId) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Environment validation
  if (!bucketName) {
    console.error('❌ NEXT_PUBLIC_GCS_BUCKET_NAME environment variable is not defined');
    return NextResponse.json({ error: 'Storage configuration error' }, { status: 500 });
  }

  if (!process.env.GOOGLE_PROJECT_ID || !process.env.VERTEX_AI_SERVICE_ACCOUNT_EMAIL || !process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY) {
    console.error('❌ Missing GCS credentials in environment variables');
    return NextResponse.json({ error: 'Storage authentication error' }, { status: 500 });
  }

  try {
    const { fileName, contentType } = await req.json();

    // Input validation
    if (!fileName || !contentType) {
      return NextResponse.json({ 
        error: 'fileName and contentType are required' 
      }, { status: 400 });
    }

    // Validate content type (only allow video files)
    const allowedVideoTypes = [
      'video/mp4', 'video/webm', 'video/ogg', 
      'video/avi', 'video/mov', 'video/wmv', 'video/quicktime'
    ];
    
    if (!allowedVideoTypes.includes(contentType)) {
      return NextResponse.json({ 
        error: 'Invalid file type. Only video files are allowed.' 
      }, { status: 400 });
    }

    // Sanitize filename
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    const uniqueFileName = `${uuidv4()}-${sanitizedFileName}`;
    
    console.log('📝 Generating signed URL for file:', {
      originalFileName: fileName,
      uniqueFileName,
      contentType,
      userId
    });

    const file = storage.bucket(bucketName).file(uniqueFileName);

    const options = {
      version: 'v4' as 'v4',
      action: 'write' as 'write',
      expires: Date.now() + 15 * 60 * 1000, // 15 minutes
      contentType: contentType,
      // Note: Removed x-goog-content-length-range to avoid header mismatch
      // File size limits are enforced in the client-side validation instead
    };

    const [url] = await file.getSignedUrl(options);
    
    console.log('✅ Signed URL generated successfully for user:', userId);
    
    return NextResponse.json({ 
      url, 
      fileName: uniqueFileName,
      expiresIn: '15 minutes'
    });
    
  } catch (error: any) {
    console.error('❌ Error generating signed URL:', {
      error: error.message,
      stack: error.stack,
      userId
    });
    
    // Provide more specific error messages based on error type
    if (error.message?.includes('credentials')) {
      return NextResponse.json({ 
        error: 'Storage authentication failed' 
      }, { status: 500 });
    }
    
    if (error.message?.includes('bucket')) {
      return NextResponse.json({ 
        error: 'Storage bucket access failed' 
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      error: 'Failed to generate signed URL. Please try again.' 
    }, { status: 500 });
  }
}
