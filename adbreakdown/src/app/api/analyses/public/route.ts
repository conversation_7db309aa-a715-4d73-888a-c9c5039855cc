import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import CacheService from '@/lib/cache'
import { OptimizedAnalysisQueries } from '@/lib/optimizedQueries'

// GET /api/analyses/public - Get public analyses with caching
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50) // Max 50 per page
    const search = searchParams.get('search') || ''
    const sort = searchParams.get('sort') || 'recent'
    const brand = searchParams.get('brand') || ''
    const celebrity = searchParams.get('celebrity') || ''
    const year = searchParams.get('year') || ''
    const duration = searchParams.get('duration') || ''
    const productCategory = searchParams.get('product_category') || ''
    const campaignCategory = searchParams.get('campaign_category') || ''
    const geography = searchParams.get('geography') || ''
    const agency = searchParams.get('agency') || ''

    // Create cache key that includes all parameters
    const cacheKey = `${page}-${limit}-${search}-${sort}-${brand}-${celebrity}-${year}-${duration}`
    
    // Check cache first (skip cache if filtering for real-time results)
    const useCache = !search && !brand && !celebrity && !year && !duration && sort === 'recent'
    if (useCache) {
      const cached = await CacheService.getPublicAnalysisList(page, limit)
      if (cached) {
        console.log(`💨 Serving cached public analyses list: page ${page}`)
        return NextResponse.json(cached, {
          headers: {
            'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600', // 30min cache, 1hr stale
          },
        })
      }
    }

    // Fetch from database using optimized query
    const result = await OptimizedAnalysisQueries.getPublicAnalysesList(page, limit, {
      search,
      sort: sort === 'sentiment' ? 'recent' : sort as 'recent' | 'popular',
      brand,
      celebrity,
      year,
      duration,
      productCategory,
      campaignCategory,
      geography,
      agency
    })
    
    if (!result) {
      return NextResponse.json(
        { error: 'Failed to fetch public analyses' },
        { status: 500 }
      )
    }

    // Format for client consumption
    const formattedAnalyses = result.analyses.map((analysis: any) => ({
      ...analysis,
      video_thumbnail_url: analysis.thumbnail_url || `https://img.youtube.com/vi/${extractYouTubeId(analysis.youtube_url)}/maxresdefault.jpg`,
      formatted_duration: formatDuration(analysis.duration_seconds),
      url: `/ad/${analysis.slug}`,
    }))

    const response = {
      ...result,
      analyses: formattedAnalyses
    }

    // Cache the result (only cache unfiltered results)
    if (useCache) {
      await CacheService.setPublicAnalysisList(page, limit, response)
    }

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600',
      },
    })

  } catch (error) {
    console.error('Error in GET /api/analyses/public:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function extractYouTubeId(url: string): string {
  if (!url) return ''
  const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
  const match = url.match(regex)
  return match ? match[1] : ''
}

function formatDuration(seconds: number): string {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.round(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}