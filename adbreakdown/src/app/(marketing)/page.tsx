'use client'

import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import Navigation from '@/components/Navigation'
import AnalysisOfTheDay from '@/components/home/<USER>'
import PublicAnalysesCarousel from '@/components/home/<USER>'
import Link from 'next/link'
import { useState, useEffect, useCallback } from 'react'
import { Sparkles, TrendingUp, Users, Zap, Target, BarChart3, ArrowRight, Search, Flame, Play } from 'lucide-react'
import Image from 'next/image'
import { useAuth } from '@/hooks/useAuth'
import { useRouter } from 'next/navigation'

const features = [
  {
    icon: Sparkles,
    title: "AI-Powered Ad Analysis",
    description:
      "Get a comprehensive, multi-faceted analysis of any YouTube ad, covering sentiment, script, visuals, and audio.",
    gradient: "from-purple-500 to-pink-500",
  },
  {
    icon: Zap,
    title: "Creative Asset Generation",
    description:
      "Instantly generate compelling marketing copy, headlines, and social media posts based on the ad's content.",
    gradient: "from-blue-500 to-cyan-500",
  },
  {
    icon: TrendingUp,
    title: "Data-Driven Recommendations",
    description:
      "Receive actionable suggestions for content improvements and precise SEO keywords to boost visibility.",
    gradient: "from-green-500 to-emerald-500",
  },
  {
    icon: Target,
    title: "Audience & Targeting Insights",
    description: "Understand the ad's target audience with demographic, psychographic, and behavioral analysis.",
    gradient: "from-orange-500 to-red-500",
  },
  {
    icon: Users,
    title: "Competitor Benchmarking",
    description: "Analyze competitor ads to understand their strategy, identify gaps, and position your brand to win.",
    gradient: "from-indigo-500 to-purple-500",
  },
  {
    icon: BarChart3,
    title: "Performance Forecasting",
    description: "Leverage machine learning to predict ad performance and get optimization tips to improve ROI.",
    gradient: "from-teal-500 to-blue-500",
  },
]

const stats = [
  { number: "50K+", label: "Ads Analyzed", sublabel: "Growing daily" },
  { number: "97%", label: "Analysis Accuracy", sublabel: "Neural precision" },
  { number: "4.2x", label: "Avg. ROI Uplift", sublabel: "Across campaigns" },
  { number: "<60s", label: "Avg. Analysis Time", sublabel: "Lightning fast" },
]

interface FeaturedAnalysis {
  id: string
  slug: string
  title: string
  video_title?: string
  inferred_brand: string
  video_url: string
  youtube_url: string
  thumbnail_url?: string
  video_thumbnail_url?: string
  overall_sentiment: number
  visual_appeal?: number
  audio_quality?: number
}

export default function Home() {
  const [videoUrl, setVideoUrl] = useState('')
  const [featuredAnalysis, setFeaturedAnalysis] = useState<FeaturedAnalysis | null>(null)
  const { isAuthenticated, loading: authLoading } = useAuth()
  const router = useRouter()

  const createPublicAnalysis = useCallback(async (youtubeUrl: string) => {
    try {
      console.log('Creating public analysis for URL:', youtubeUrl)
      const response = await fetch('/api/analyses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ youtubeUrl }),
      })

      if (!response.ok) {
        throw new Error('Failed to create analysis')
      }

      const data = await response.json()
      console.log('Analysis created:', data)
      
      // Redirect to the analysis page
      if (data.analysis_id) {
        router.push(`/ad/${data.analysis_id}`)
      } else {
        console.error('No analysis ID returned')
        router.push('/studio')
      }
    } catch (error) {
      console.error('Error creating analysis:', error)
      // Fallback to studio page
      router.push('/studio')
    }
  }, [router])

  useEffect(() => {
    fetchFeaturedAnalysis()
  }, [])

  // Handle pending analysis after sign-in - create public analysis
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      const pendingUrl = localStorage.getItem('pendingAnalysisUrl')
      if (pendingUrl) {
        localStorage.removeItem('pendingAnalysisUrl')
        createPublicAnalysis(pendingUrl)
      }
    }
  }, [isAuthenticated, authLoading, router, createPublicAnalysis])

  const fetchFeaturedAnalysis = async () => {
    try {
      const response = await fetch('/api/featured/simple')
      if (response.ok) {
        const data = await response.json()
        console.log('Featured API response:', data)
        if (data.current && data.current.analysis) {
          console.log('Featured analysis data:', data.current.analysis)
          setFeaturedAnalysis(data.current.analysis)
        } else {
          console.log('No featured analysis found in response')
        }
      } else {
        console.log('Featured API response not ok:', response.status)
      }
    } catch (error) {
      console.error('Error fetching featured analysis:', error)
    }
  }

  const handleAnalyzeAd = () => {
    if (!videoUrl.trim()) return

    // Check if user is authenticated
    if (!isAuthenticated) {
      // Store the video URL in localStorage so we can use it after sign-in
      try {
        localStorage.setItem('pendingAnalysisUrl', videoUrl)
      } catch (error) {
        console.warn('localStorage not available:', error)
        // Continue with sign-in even if localStorage fails
      }
      // Redirect to sign-in page with return URL parameter for better UX
      const returnUrl = encodeURIComponent(`/studio?analyze=true`)
      router.push(`/sign-in?redirect_url=${returnUrl}`)
      return
    }

    // User is authenticated, create public analysis immediately
    createPublicAnalysis(videoUrl)
  }

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  const getYouTubeThumbnail = (videoId: string): string => {
    return `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`
  }

  const getThumbnailUrl = (): string => {
    console.log('Getting thumbnail URL for:', featuredAnalysis)
    
    // Try to get thumbnail from database first (check both possible field names)
    if (featuredAnalysis?.thumbnail_url) {
      console.log('Using database thumbnail_url:', featuredAnalysis.thumbnail_url)
      return featuredAnalysis.thumbnail_url
    }
    
    if (featuredAnalysis?.video_thumbnail_url) {
      console.log('Using database video_thumbnail_url:', featuredAnalysis.video_thumbnail_url)
      return featuredAnalysis.video_thumbnail_url
    }
    
    // Extract from YouTube video URL
    if (featuredAnalysis?.youtube_url || featuredAnalysis?.video_url) {
      const url = featuredAnalysis.youtube_url || featuredAnalysis.video_url
      const videoId = extractYouTubeVideoId(url)
      if (videoId) {
        const thumbnailUrl = getYouTubeThumbnail(videoId)
        console.log('Using YouTube thumbnail:', thumbnailUrl)
        return thumbnailUrl
      }
    }
    
    // Use data URL fallback that doesn't need external files
    console.log('Using fallback placeholder')
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIyNSIgdmlld0JveD0iMCAwIDQwMCAyMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjI1IiBmaWxsPSIjM0I4MkY2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTEyLjUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSI+RmVhdHVyZWQgQW5hbHlzaXM8L3RleHQ+Cjwvc3ZnPgo='
  }


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <Navigation />

      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-400/20 to-orange-600/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <main className="relative container mx-auto px-6 py-20">
        {/* Hero Section */}
        <div className="text-center mb-32">
          <div className="mb-8">
            <Badge
              variant="secondary"
              className="px-6 py-2 text-sm font-medium bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200/50 hover:border-blue-300/50 transition-colors"
            >
              <Sparkles className="w-4 h-4 mr-2" />
              AI Powered Ad Analysis
            </Badge>
          </div>

          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent">
              Your Guide to
            </span>
            <br />
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Better Ads
            </span>
          </h1>

          <p className="text-md lg:text-xl mb-12 max-w-3xl mx-auto leading-relaxed text-gray-600 font-light">
            Go from subjective feedback to objective, data-driven insights. Understand what works in your video ads, why
            it works, and how to make it better.{" "}
            <span className="font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Instantly
            </span>
            .
          </p>

             {/* URL Input Section */}
          <div className="max-w-4xl mx-auto mb-12">
            <div className="bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-8 shadow-xl">
              <div className="text-center mb-6">

                <p className="text-gray-600">
                  Paste a YouTube video URL to get instant AI-powered insights
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="url"
                  placeholder="https://www.youtube.com/watch?v=..."
                  value={videoUrl}
                  onChange={(e) => setVideoUrl(e.target.value)}
                  className="flex-1 h-12 text-base border-gray-300 rounded-xl focus:border-blue-500 focus:ring-blue-500"
                />
                <Button
                  onClick={handleAnalyzeAd}
                  disabled={!videoUrl.trim() || authLoading}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 h-12 text-base font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
                >
                  <Search className="w-5 h-5 mr-2" />
                  {authLoading ? 'Loading...' : (isAuthenticated ? 'Analyse Ad' : 'Sign In to Analyse')}
                </Button>
              </div>
            </div>
          </div>

       

        {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Link href="/studio">
              <Button 
                size="lg" 
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-full shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                Get Started
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>


        {/* Featured Analysis Section */}
        <div className="mb-32">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row gap-8 bg-white/60 backdrop-blur-sm border border-gray-200/50 rounded-2xl p-8 shadow-lg items-center">
            {/* Thumbnail Section - 1/4 width */}
            <div className="w-full md:w-1/4 flex-shrink-0">
              <Link href="/featured">
                <div className="relative aspect-video rounded-xl overflow-hidden shadow-md cursor-pointer group">
                  <Image
                    src={getThumbnailUrl()}
                    alt={featuredAnalysis?.title || 'Featured Analysis'}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    unoptimized={!featuredAnalysis || (!featuredAnalysis.thumbnail_url && !featuredAnalysis.video_thumbnail_url && !featuredAnalysis.youtube_url && !featuredAnalysis.video_url)}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement
                      // Use a data URL fallback that doesn't require external domains
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjIyNSIgdmlld0JveD0iMCAwIDQwMCAyMjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjI1IiBmaWxsPSIjM0I4MkY2Ii8+Cjx0ZXh0IHg9IjIwMCIgeT0iMTEyLjUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSI+RmVhdHVyZWQgQW5hbHlzaXM8L3RleHQ+Cjwvc3ZnPgo='
                    }}
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                    <div className="bg-white bg-opacity-90 rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                      <Play className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                </div>
              </Link>
            </div>

            
            
            {/* Text Section - 3/4 width */}
            <div className="w-full md:w-3/4">
              <div className="flex items-center text-center gap-2 mb-2">
                <Flame className="w-4 h-4 text-pink-600" />
                <span className="text-pink-600 font-semibold text-xs uppercase tracking-wide">
                  Featured Ad breakdown
                </span>
              </div>
              
             {/*}
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                {featuredAnalysis?.title || 'Featured Analysis'}
              </h2>
              */}
              
              <p className="text-gray-700 mb-6 text-sm  leading-relaxed">
                Get a look at a complete ad breakdown for <span className="font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  {featuredAnalysis?.title || 'this featured analysis'}
                </span>. 
                Experience how our AI analyzes emotions, targeting, script effectiveness, and creative elements in real campaigns.
              </p>
              
              <div>
                <Link href="/featured">
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="border-gray-300 hover:border-gray-400 text-gray-700 hover:text-gray-900 font-medium"
                  >
                    View Full Analysis
                    <ArrowRight className="w-3 h-3 ml-2" />
                  </Button>
                </Link>
              </div>
            </div>
            </div>
          </div>
        </div>

       
       
        </div>



        {/* Features Grid */}
        <div className="mb-32">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Powerful Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Everything you need to analyze, optimize, and scale your video advertising
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <Card
                  key={index}
                  className="group relative bg-white/60 backdrop-blur-sm border border-gray-200/50 hover:border-gray-300/50 hover:shadow-2xl transition-all duration-500 cursor-pointer rounded-2xl p-8 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-gray-50/80 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <CardHeader className="relative p-0">
                    <div
                      className={`w-14 h-14 bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-6 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}
                    >
                      <IconComponent className="w-7 h-7 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold text-gray-900 mb-4 group-hover:text-gray-800 transition-colors">
                      {feature.title}
                    </CardTitle>
                    <CardDescription className="text-gray-600 text-base leading-relaxed group-hover:text-gray-700 transition-colors">
                      {feature.description}
                    </CardDescription>
                  </CardHeader>
                </Card>
              )
            })}
          </div>
        </div>


        {/* Analysis of the Day 
        <div className="mb-32">
          <AnalysisOfTheDay />
        </div>

        */}

        {/* Public Analyses Carousel */}
        <div className="mb-32">
          <PublicAnalysesCarousel />
        </div>

       
        {/* Stats Section */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 rounded-3xl"></div>
          <div className="relative bg-white/40 backdrop-blur-sm border border-gray-200/50 rounded-3xl p-12">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                Trusted by Thousands
              </h2>
              <p className="text-gray-600 text-lg">Join the growing community of marketers who trust our AI</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
              {stats.map((stat, index) => (
                <div key={index} className="group">
                  <div className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3 group-hover:scale-110 transition-transform duration-300">
                    {stat.number}
                  </div>
                  <div className="font-bold mb-2 text-gray-900 text-lg">{stat.label}</div>
                  <div className="text-gray-600">{stat.sublabel}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}