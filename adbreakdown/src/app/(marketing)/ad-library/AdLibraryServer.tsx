import { Suspense } from 'react'
import AdLibraryClient from '@/components/ad-library/AdLibraryClient'
import Navigation from '@/components/Navigation'
import { createClient } from '@supabase/supabase-js'

// Use service role key for public data access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function getInitialData() {
  try {
    // Fetch initial analyses and total count in parallel
    const [analysesRes, countRes] = await Promise.all([
      supabase
        .from('ad_analyses')
        .select(`
          id, slug, title, brand, thumbnail_url, duration_seconds, 
          created_at, analysis_completed_at, youtube_video_id, is_public, showcase, celebrity, campaign_category
        `)
        .eq('is_public', true)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .range(0, 11),
      supabase
        .from('ad_analyses')
        .select('*', { count: 'exact', head: true })
        .eq('is_public', true)
        .eq('status', 'completed')
    ]);

    const { data: analyses, error: analysesError } = analysesRes;
    const { count } = countRes;

    if (analysesError) {
      throw new Error(analysesError.message);
    }

    // Fetch all distinct filter options in parallel
    const [
      brandsRes,
      productCategoriesRes,
      campaignCategoriesRes,
      geographiesRes,
      agenciesRes,
      yearsRes,
      celebritiesRes
    ] = await Promise.all([
      supabase.from('ad_analyses').select('brand').eq('is_public', true).eq('status', 'completed').not('brand', 'is', null),
      supabase.from('ad_analyses').select('product_category').eq('is_public', true).eq('status', 'completed').not('product_category', 'is', null),
      supabase.from('ad_analyses').select('campaign_category').eq('is_public', true).eq('status', 'completed').not('campaign_category', 'is', null),
      supabase.from('ad_analyses').select('geography').eq('is_public', true).eq('status', 'completed').not('geography', 'is', null),
      supabase.from('ad_analyses').select('agency').eq('is_public', true).eq('status', 'completed').not('agency', 'is', null),
      supabase.from('ad_analyses').select('launch_date').eq('is_public', true).eq('status', 'completed').not('launch_date', 'is', null),
      supabase.from('ad_analyses').select('celebrity').eq('is_public', true).eq('status', 'completed').not('celebrity', 'is', null)
    ]);

    const getUniqueValues = (response: any, key: string): string[] => [...new Set(response.data?.map((item: any) => item[key]).filter(Boolean) || [])].filter((value): value is string => typeof value === 'string');
    
    const brands = getUniqueValues(brandsRes, 'brand');
    const productCategories = getUniqueValues(productCategoriesRes, 'product_category');
    const campaignCategories = getUniqueValues(campaignCategoriesRes, 'campaign_category');
    const geographies = getUniqueValues(geographiesRes, 'geography');
    const agencies = getUniqueValues(agenciesRes, 'agency');
    const years = [...new Set(yearsRes.data?.map((item: any) => new Date(item.launch_date).getFullYear().toString()).filter(Boolean) || [])];
    const celebrities = getUniqueValues(celebritiesRes, 'celebrity');

    // Format analyses for client
    const formattedAnalyses = (analyses || []).map((analysis: any) => ({
      ...analysis,
      // Map brand to inferred_brand for compatibility with frontend
      inferred_brand: analysis.brand,
      video_thumbnail_url: analysis.thumbnail_url,
      duration_formatted: formatDuration(analysis.duration_seconds),
      url: `/ad/${analysis.slug}`,
    }));

    const pagination = {
      page: 1,
      limit: 12,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / 12),
      hasNext: (count || 0) > 12,
      hasPrev: false
    };

    return {
      analyses: formattedAnalyses,
      pagination,
      filters: {
        brands: brands.sort(),
        productCategories: productCategories.sort(),
        campaignCategories: campaignCategories.sort(),
        geographies: geographies.sort(),
        agencies: agencies.sort(),
        years: years.sort().reverse(),
        celebrities: celebrities.sort()
      }
    };
  } catch (error) {
    console.error('Error fetching initial ad library data:', error);
    // Return a default empty state on error
    return {
      analyses: [],
      pagination: { page: 1, limit: 12, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
      filters: { brands: [], productCategories: [], campaignCategories: [], geographies: [], agencies: [], years: [], celebrities: [] }
    };
  }
}

function formatDuration(seconds: number): string {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.round(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

export default async function AdLibraryServer() {
  const initialData = await getInitialData()

  return (
    <div className="flex flex-1 flex-col gap-4">
      <Navigation />
      <main className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Enhanced Header Section 
        <div className="mb-8">
          <div className="bg-gradient-to-br from-blue-50 via-white to-purple-50 border border-blue-100 rounded-xl p-8">
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Ad Library
              </h1>
              <p className="text-lg text-gray-600 max-w-4xl mx-auto mb-6">
                Discover AI-powered insights from video ad analyses curated by our community. 
                Analyze any YouTube ad instantly or explore successful advertising strategies, creative approaches, and audience engagement techniques.
              </p>
            </div>
          </div>
        </div>

        */}

        <Suspense fallback={<div>Loading...</div>}>
          <AdLibraryClient 
            initialAnalyses={initialData.analyses}
            initialPagination={initialData.pagination}
            initialFilters={initialData.filters}
          />
        </Suspense>
      </main>
    </div>
  )
}