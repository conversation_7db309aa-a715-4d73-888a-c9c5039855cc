
'use client'

import React, { Suspense, lazy } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

const LazyYouTube = lazy(() => import('react-youtube'))

interface VideoPlayerProps {
  youtubeVideoId: string | null
  videoMetadata: {
    thumbnail: string
    title: string
    duration: string
  }
  isLoading?: boolean
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  youtubeVideoId,
  videoMetadata,
  isLoading = false
}) => {
  const youtubeOpts = {
    height: '100%',
    width: '100%',
    playerVars: {
      autoplay: 0,
      modestbranding: 1,
      rel: 0,
    },
  }

  return (
    <Card className="h-full w-full">
      <CardContent className="p-0">
        <div className="relative bg-black rounded-lg overflow-hidden aspect-video">
          {youtubeVideoId ? (
            <div className="absolute inset-0">
              <Suspense fallback={<Skeleton className="w-full h-full" />}>
                <LazyYouTube
                  videoId={youtubeVideoId}
                  opts={youtubeOpts}
                  className="w-full h-full"
                  iframeClassName="w-full h-full rounded-lg"
                />
              </Suspense>
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin h-5 w-5 border-2 border-gray-400 border-t-transparent rounded-full"></div>
                  Fetching video...
                </div>
              ) : (
                "Video not available"
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default VideoPlayer
