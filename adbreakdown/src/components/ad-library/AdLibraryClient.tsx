'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ChevronLeft, ChevronRight, Search, Plus, BarChart3, AlertTriangle, Filter, X, User } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import AdTileGrid from '@/components/studio/AdTileGrid'

interface PublicAnalysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  thumbnail_url: string
  video_thumbnail_url: string
  duration_seconds: number
  duration_formatted: string
  created_at: string
  analysis_completed_at: string
  youtube_video_id: string
  is_public: boolean
  overall_sentiment?: number
  showcase?: boolean
  view_count?: number
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

interface AdLibraryClientProps {
  initialAnalyses: PublicAnalysis[]
  initialPagination: PaginationInfo
  initialFilters: {
    brands: string[]
    productCategories: string[]
    campaignCategories: string[]
    geographies: string[]
    agencies: string[]
    years: string[]
    celebrities: string[]
  }
}

function AdLibraryClient({ 
  initialAnalyses, 
  initialPagination, 
  initialFilters 
}: AdLibraryClientProps) {
  const { isAuthenticated } = useAuth()
  const searchParams = useSearchParams()
  
  // Public Analyses State
  const [analyses, setAnalyses] = useState<PublicAnalysis[]>(initialAnalyses)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [pagination, setPagination] = useState<PaginationInfo>(initialPagination)

  // Filters and Search
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'recent' | 'popular'>('recent')
  const [selectedBrand, setSelectedBrand] = useState<string>('all')
  const [selectedProductCategory, setSelectedProductCategory] = useState<string>('all')
  const [selectedCampaignCategory, setSelectedCampaignCategory] = useState<string>('all')
  const [selectedGeography, setSelectedGeography] = useState<string>('all')
  const [selectedAgency, setSelectedAgency] = useState<string>('all')
  const [selectedYear, setSelectedYear] = useState<string>('all')
  const [selectedCelebrity, setSelectedCelebrity] = useState<string>('all')
  const [selectedDuration, setSelectedDuration] = useState<string>('all')
  
  // Filter options
  const [availableBrands] = useState<string[]>(initialFilters.brands)
  const [availableYears] = useState<string[]>(initialFilters.years)
  
  // Analysis input state
  const [analysisUrl, setAnalysisUrl] = useState('')
  const [analysisLoading, setAnalysisLoading] = useState(false)
  const [analysisError, setAnalysisError] = useState('')
  
  // Filter modal and view state
  const [showFilterModal, setShowFilterModal] = useState(false)
  const [viewMode, setViewMode] = useState<'all' | 'yours'>(() => {
    const viewParam = searchParams.get('view')
    return (viewParam === 'yours' && isAuthenticated) ? 'yours' : 'all'
  })
  const [availableDurations] = useState([
    { value: 'short', label: 'Short (≤30s)' },
    { value: 'medium', label: 'Medium (31-60s)' },
    { value: 'long', label: 'Long (60s+)' }
  ])

  // Helper functions for filter management
  const getActiveFilterCount = () => {
    let count = 0
    if (selectedBrand !== 'all') count++
    if (selectedProductCategory !== 'all') count++
    if (selectedCampaignCategory !== 'all') count++
    if (selectedGeography !== 'all') count++
    if (selectedAgency !== 'all') count++
    if (selectedCelebrity !== 'all') count++
    if (selectedYear !== 'all') count++
    if (selectedDuration !== 'all') count++
    return count
  }

  const getActiveFilterTags = () => {
    const tags: { key: string; label: string; value: string }[] = []
    
    if (selectedBrand !== 'all') {
      tags.push({ key: 'brand', label: 'Brand', value: selectedBrand })
    }
    if (selectedProductCategory !== 'all') {
      tags.push({ key: 'productCategory', label: 'Product', value: selectedProductCategory })
    }
    if (selectedCampaignCategory !== 'all') {
      tags.push({ key: 'campaignCategory', label: 'Campaign', value: selectedCampaignCategory })
    }
    if (selectedGeography !== 'all') {
      tags.push({ key: 'geography', label: 'Geography', value: selectedGeography })
    }
    if (selectedAgency !== 'all') {
      tags.push({ key: 'agency', label: 'Agency', value: selectedAgency })
    }
    if (selectedCelebrity !== 'all') {
      tags.push({ key: 'celebrity', label: 'Celebrity', value: selectedCelebrity })
    }
    if (selectedYear !== 'all') {
      tags.push({ key: 'year', label: 'Year', value: selectedYear })
    }
    if (selectedDuration !== 'all') {
      const durationLabel = availableDurations.find(d => d.value === selectedDuration)?.label || selectedDuration
      tags.push({ key: 'duration', label: 'Duration', value: durationLabel })
    }
    
    return tags
  }

  const clearFilter = (filterKey: string) => {
    switch (filterKey) {
      case 'brand':
        setSelectedBrand('all')
        break
      case 'productCategory':
        setSelectedProductCategory('all')
        break
      case 'campaignCategory':
        setSelectedCampaignCategory('all')
        break
      case 'geography':
        setSelectedGeography('all')
        break
      case 'agency':
        setSelectedAgency('all')
        break
      case 'celebrity':
        setSelectedCelebrity('all')
        break
      case 'year':
        setSelectedYear('all')
        break
      case 'duration':
        setSelectedDuration('all')
        break
    }
  }

  const clearAllFilters = () => {
    setSelectedBrand('all')
    setSelectedProductCategory('all')
    setSelectedCampaignCategory('all')
    setSelectedGeography('all')
    setSelectedAgency('all')
    setSelectedCelebrity('all')
    setSelectedYear('all')
    setSelectedDuration('all')
    setSearchQuery('')
  }

  const fetchAnalyses = useCallback(async (
    page: number = 1, 
    search: string = '', 
    sort: string = 'recent', 
    brand: string = 'all',
    productCategory: string = 'all',
    campaignCategory: string = 'all',
    geography: string = 'all',
    agency: string = 'all',
    celebrity: string = 'all',
    year: string = 'all',
    duration: string = 'all'
  ) => {
    setLoading(true)
    setError('')
    
    try {
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...(search && { search }),
        sort,
        ...(brand && brand !== 'all' && { brand }),
        ...(celebrity && celebrity !== 'all' && { celebrity }),
        ...(year && year !== 'all' && { year }),
        ...(duration && duration !== 'all' && { duration }),
        ...(productCategory && productCategory !== 'all' && { product_category: productCategory }),
        ...(campaignCategory && campaignCategory !== 'all' && { campaign_category: campaignCategory }),
        ...(geography && geography !== 'all' && { geography }),
        ...(agency && agency !== 'all' && { agency })
      })
      
      // Use different endpoint based on view mode
      const endpoint = viewMode === 'yours' ? '/api/analyses' : '/api/analyses/public'
      const response = await fetch(`${endpoint}?${searchParams}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch analyses')
      }
      
      const data = await response.json()
      setAnalyses(data.analyses || [])
      setPagination(data.pagination)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analyses')
    } finally {
      setLoading(false)
    }
  }, [viewMode])

  useEffect(() => {
    const hasFilters = searchQuery || sortBy !== 'recent' || selectedBrand !== 'all' || 
                      selectedProductCategory !== 'all' || selectedCampaignCategory !== 'all' || 
                      selectedGeography !== 'all' || selectedAgency !== 'all' || selectedCelebrity !== 'all' || 
                      selectedYear !== 'all' || selectedDuration !== 'all'
    
    if (hasFilters || viewMode === 'yours') {
      fetchAnalyses(pagination.page, searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration)
    }
  }, [searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration, pagination.page, viewMode, fetchAnalyses])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, page: 1 }))
    fetchAnalyses(1, searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration)
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
    fetchAnalyses(newPage, searchQuery, sortBy, selectedBrand, selectedProductCategory, selectedCampaignCategory, selectedGeography, selectedAgency, selectedCelebrity, selectedYear, selectedDuration)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const handleAnalyzeVideo = async () => {
    if (!analysisUrl.trim()) {
      setAnalysisError('Please enter a YouTube URL')
      return
    }

    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([A-Za-z0-9_-]{11})/
    if (!youtubeRegex.test(analysisUrl)) {
      setAnalysisError('Please enter a valid YouTube URL')
      return
    }

    try {
      setAnalysisLoading(true)
      setAnalysisError('')

      const response = await fetch('/api/analyses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ youtubeUrl: analysisUrl })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create analysis')
      }

      if (data.analysis?.slug) {
        window.location.href = `/ad/${data.analysis.slug}`
      } else if (data.slug) {
        window.location.href = `/ad/${data.slug}`
      } else {
        window.location.href = `/ad/${data.analysis_id}`
      }

    } catch (error: any) {
      console.error('Error creating analysis:', error)
      setAnalysisError(error.message || 'An error occurred while processing your request')
    } finally {
      setAnalysisLoading(false)
    }
  }

  return (
    <>
      {/* Hero Section - Ad Library */}
      <div className="bg-gradient-to-br from-indigo-400 via-purple-500 to-indigo-500 rounded-3xl p-8 mb-8 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Ad Library
          </h1>
          <p className="text-xl opacity-90 mb-8 max-w-3xl mx-auto">
            Discover AI-powered insights from video ad analyses curated by our community. Learn from successful advertising strategies, creative approaches, and audience engagement techniques.
          </p>
          
          {/* Analysis Input Card */}
          <div className="bg-white/15 backdrop-blur-lg border border-white/20 rounded-2xl p-4 mb-4">
            <div className="text-white font-semibold mb-4">
              🔍 Paste any YouTube ad URL to start analyzing
            </div>
            <div className="flex flex-col sm:flex-row gap-3 max-w-2xl mx-auto mb-4">
              <input
                type="url"
                placeholder="https://www.youtube.com/watch?v=..."
                value={analysisUrl}
                onChange={(e) => {
                  setAnalysisUrl(e.target.value)
                  setAnalysisError('')
                }}
                className="flex-1 h-10 px-6 text-lg border-2 border-white/30 rounded-xl focus:outline-none focus:border-white/80 bg-white/90 text-gray-800 placeholder-gray-500 transition-all duration-300"
                disabled={analysisLoading}
              />
              <button 
                onClick={handleAnalyzeVideo}
                disabled={analysisLoading || !analysisUrl.trim()}
                className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 disabled:opacity-50 text-white h-10 px-8 rounded-xl font-semibold text-lg transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg flex items-center justify-center gap-2 min-w-[180px]"
              >
                {analysisLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    🚀 Analyze Now
                  </>
                )}
              </button>
            </div>
            <div className="text-white/80 text-sm mb-1">
              💡 Works with any public YouTube video - ads, commercials, or promotional content (less than 3 mins)
            </div>
            {analysisError && (
              <div className="flex items-center justify-center gap-2 p-3 bg-red-500/20 border border-red-400/30 rounded-xl text-red-100 max-w-2xl mx-auto">
                <AlertTriangle className="h-4 w-4 flex-shrink-0" />
                <p className="text-sm">{analysisError}</p>
              </div>
            )}
            
            {/* Quick Examples 
            <div className="mt-6">
              <div className="text-sm font-semibold text-white/90 mb-3">
                📺 Try these popular ads:
              </div>
              <div className="flex flex-wrap justify-center gap-3">
                <button 
                  className="text-sm bg-white/10 hover:bg-white/20 text-white/90 hover:text-white px-4 py-2 rounded-lg transition-all duration-300 border border-white/20"
                  onClick={() => setAnalysisUrl('https://www.youtube.com/watch?v=WYP9AGtLvRg')}
                >
                  Nike &ldquo;Just Do It&rdquo; Campaign
                </button>
                <button 
                  className="text-sm bg-white/10 hover:bg-white/20 text-white/90 hover:text-white px-4 py-2 rounded-lg transition-all duration-300 border border-white/20"
                  onClick={() => setAnalysisUrl('https://www.youtube.com/watch?v=39_OWsTtzBE')}
                >
                  Apple iPhone Launch
                </button>
                <button 
                  className="text-sm bg-white/10 hover:bg-white/20 text-white/90 hover:text-white px-4 py-2 rounded-lg transition-all duration-300 border border-white/20"
                  onClick={() => setAnalysisUrl('https://www.youtube.com/watch?v=hFDcoX7s6rE')}
                >
                  Coca-Cola Holiday Ad
                </button>
                <button 
                  className="text-sm bg-white/10 hover:bg-white/20 text-white/90 hover:text-white px-4 py-2 rounded-lg transition-all duration-300 border border-white/20"
                  onClick={() => setAnalysisUrl('https://www.youtube.com/watch?v=nLwML2PagbY')}
                >
                  Super Bowl Commercial
                </button>
              </div>
            </div>
            */}


          </div>
          
          {/* Additional Info for Authenticated Users */}
          {isAuthenticated && (
            <div className="bg-blue-500/20 backdrop-blur-sm border border-blue-300/30 rounded-xl p-4">
              <p className="text-blue-100 text-sm">
                ✨ As a member, your analyses will be added to the public library for the community to learn from
              </p>
            </div>
          )}
        </div>
      </div>


      {/* Content Section Header */}
      <div className="bg-white rounded-2xl p-2 mb-4 border border-gray-200 shadow-sm">
        <div className="flex items-center gap-4 flex-wrap">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-white rounded-lg p-1 border">
            <Button
              variant={viewMode === 'all' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('all')}
              className="text-xs"
            >
              All Ads
            </Button>
            {isAuthenticated && (
              <Button
                variant={viewMode === 'yours' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('yours')}
                className="text-xs"
              >
                <User className="h-3 w-3 mr-1" />
                Your Curation
              </Button>
            )}
          </div>
          
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex gap-2 flex-1 max-w-md">
            <Input
              type="text"
              placeholder="Search by brand, title, or content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1 min-w-[200px]"
            />
            <Button type="submit" variant="outline" size="sm">
              <Search className="h-4 w-4" />
            </Button>
          </form>
          
          <div className="flex items-center gap-2 ml-auto">
            {/* Sort Dropdown */}
            <Select
              value={sortBy}
              onValueChange={(value) => setSortBy(value as 'recent' | 'popular')}
            >
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="popular">Most Popular</SelectItem>
              </SelectContent>
            </Select>
            
            {/* Filter Button */}
            <Button
              variant="outline"
              onClick={() => setShowFilterModal(true)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
              {/* Active Filter Count */}
              {getActiveFilterCount() > 0 && (
                <Badge variant="destructive" className="ml-1 px-1.5 py-0 text-xs">
                  {getActiveFilterCount()}
                </Badge>
              )}
            </Button>
          </div>
        </div>
        
        {/* Active Filters Tags */}
        {getActiveFilterTags().length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            <span className="text-sm text-gray-600 mr-2">Active filters:</span>
            {getActiveFilterTags().map((tag) => (
              <Badge key={tag.key} variant="secondary" className="flex items-center gap-1">
                {tag.label}: {tag.value}
                <X 
                  className="h-3 w-3 cursor-pointer hover:text-red-600" 
                  onClick={() => clearFilter(tag.key)}
                />
              </Badge>
            ))}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-xs text-red-600 hover:text-red-700 h-6 px-2"
            >
              Clear all
            </Button>
          </div>
        )}
      </div>
      
      {/* Filter Modal */}
      {showFilterModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Filters</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilterModal(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Brand Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Brand</label>
                  <Select value={selectedBrand} onValueChange={setSelectedBrand}>
                    <SelectTrigger>
                      <SelectValue placeholder="All brands" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All brands</SelectItem>
                      {availableBrands.map((brand) => (
                        <SelectItem key={brand} value={brand}>
                          {brand}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Product Category Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Product Category</label>
                  <Select value={selectedProductCategory} onValueChange={setSelectedProductCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="All products" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Products</SelectItem>
                      {initialFilters.productCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Campaign Category Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Campaign Category</label>
                  <Select value={selectedCampaignCategory} onValueChange={setSelectedCampaignCategory}>
                    <SelectTrigger>
                      <SelectValue placeholder="All campaigns" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Campaigns</SelectItem>
                      {initialFilters.campaignCategories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Geography Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Geography</label>
                  <Select value={selectedGeography} onValueChange={setSelectedGeography}>
                    <SelectTrigger>
                      <SelectValue placeholder="All geographies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Geographies</SelectItem>
                      {initialFilters.geographies.map((geo) => (
                        <SelectItem key={geo} value={geo}>
                          {geo}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Agency Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Agency</label>
                  <Select value={selectedAgency} onValueChange={setSelectedAgency}>
                    <SelectTrigger>
                      <SelectValue placeholder="All agencies" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Agencies</SelectItem>
                      {initialFilters.agencies.map((agency) => (
                        <SelectItem key={agency} value={agency}>
                          {agency}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Celebrity Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Celebrity</label>
                  <Select value={selectedCelebrity} onValueChange={setSelectedCelebrity}>
                    <SelectTrigger>
                      <SelectValue placeholder="All celebrities" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Celebrities</SelectItem>
                      {initialFilters.celebrities.map((celebrity) => (
                        <SelectItem key={celebrity} value={celebrity}>
                          {celebrity}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Year Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Year</label>
                  <Select value={selectedYear} onValueChange={setSelectedYear}>
                    <SelectTrigger>
                      <SelectValue placeholder="All years" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All years</SelectItem>
                      {availableYears.map((year) => (
                        <SelectItem key={year} value={year}>
                          {year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Duration Filter */}
                <div>
                  <label className="block text-sm font-medium mb-2">Duration</label>
                  <Select value={selectedDuration} onValueChange={setSelectedDuration}>
                    <SelectTrigger>
                      <SelectValue placeholder="All durations" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All durations</SelectItem>
                      {availableDurations.map((duration) => (
                        <SelectItem key={duration.value} value={duration.value}>
                          {duration.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="flex justify-between mt-6">
                <Button
                  variant="outline"
                  onClick={clearAllFilters}
                >
                  Reset All Filters
                </Button>
                <Button
                  onClick={() => setShowFilterModal(false)}
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Results Stats */}
      {!loading && (
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between text-sm text-gray-600">
            <span>
              Showing {analyses.length} of {pagination.total} {viewMode === 'yours' ? 'your analyses' : 'analyses'}
            </span>
            {searchQuery && (
              <span>
                Search results for &quot;{searchQuery}&quot;
              </span>
            )}
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
          {error}
        </div>
      )}

      {/* Analyses Grid */}
      {loading ? (
        <AdTileGrid 
          analyses={[]} 
          loading={true}
          showFeatured={false}
          showPrivacyBadge={false}
          columns={3}
          className="mb-8"
        />
      ) : analyses.length === 0 ? (
        <div className="text-center py-16 bg-gray-50 rounded-lg">
          <div className="max-w-md mx-auto">
            <h3 className="text-lg font-medium mb-2 text-gray-900">
              {searchQuery ? 'No search results found' : 'No analyses found'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery 
                ? `Try adjusting your search terms or browse all analyses.`
                : 'Be the first to create and share an ad analysis with the community!'
              }
            </p>
            <div className="flex gap-3 justify-center">
              {searchQuery ? (
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('')
                    setSelectedBrand('all')
                    setSelectedProductCategory('all')
                    setSelectedCampaignCategory('all')
                    setSelectedGeography('all')
                    setSelectedAgency('all')
                    setSelectedCelebrity('all')
                    setSelectedYear('all')
                    setSelectedDuration('all')
                    fetchAnalyses(1, '', sortBy, 'all', 'all', 'all', 'all', 'all', 'all', 'all', 'all')
                  }}
                >
                  Clear Search
                </Button>
              ) : (
                <Link href={isAuthenticated ? "/studio" : "/sign-up"}>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-1" />
                    {isAuthenticated ? 'Create Analysis' : 'Get Started'}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      ) : (
        <>
          <AdTileGrid 
            analyses={analyses} 
            loading={false}
            showFeatured={false}
            showPrivacyBadge={false}
            columns={3}
            className="mb-8"
          />

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 mb-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrev}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {[...Array(Math.min(5, pagination.totalPages))].map((_, i) => {
                  const pageNum = pagination.page <= 3 
                    ? i + 1 
                    : pagination.page >= pagination.totalPages - 2
                      ? pagination.totalPages - 4 + i
                      : pagination.page - 2 + i

                  if (pageNum < 1 || pageNum > pagination.totalPages) return null

                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === pagination.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      className="w-10"
                    >
                      {pageNum}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </>
      )}

      {/* Call to Action */}
      {!isAuthenticated && analyses.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-3">
            Ready to Analyze Your Own Ads?
          </h3>
          <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
            Join breakdown.ad to create AI-powered analyses of your video ads, 
            gain actionable insights, and share your findings with the community.
          </p>
          <div className="flex gap-3 justify-center">
            <Link href="/sign-up">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/featured">
              <Button size="lg" variant="outline">
                View Featured Analysis
              </Button>
            </Link>
          </div>
        </div>
      )}
    </>
  )
}

export default AdLibraryClient