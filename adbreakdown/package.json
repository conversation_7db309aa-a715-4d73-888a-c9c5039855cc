{"name": "breakdown.ad", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^6.23.1", "@google-cloud/storage": "^7.16.0", "@google-cloud/vertexai": "^1.10.0", "@google/genai": "^1.10.0", "@google/generative-ai": "^0.24.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.38.4", "babel-plugin-react-compiler": "^19.1.0-rc.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "eslint-plugin-react-compiler": "^19.1.0-rc.2", "html2pdf.js": "^0.10.3", "lucide-react": "^0.525.0", "next": "^15.3.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-youtube": "^10.1.0", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "svix": "^1.15.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "video.js": "^8.6.1", "videojs-contrib-hls": "^5.15.0", "yt-dlp-wrap": "^2.3.12", "ytdl-core": "^4.11.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@types/video.js": "^7.3.58", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.2.25", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}