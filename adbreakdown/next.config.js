/** @type {import('next').NextConfig} */
const nextConfig = {
    experimental: {
        reactCompiler: true,
        serverComponentsExternalPackages: ['@google-cloud/storage'],
    },
    // Configure body size limits for file uploads
    api: {
        bodyParser: {
            sizeLimit: '100mb',
        },
    },
    // Exclude supabase functions from build
    webpack: (config) => {
        config.externals = config.externals || [];
        config.externals.push({
            'https://deno.land/std@0.177.0/http/server.ts': 'commonjs https://deno.land/std@0.177.0/http/server.ts',
        });
        return config;
    },
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'i.ytimg.com',
            },
            {
                protocol: 'https',
                hostname: 'img.youtube.com',
            },
            {
                protocol: 'https',
                hostname: 'placehold.co',
            }
        ],
    },
    // Enable static exports for truly static pages
    output: process.env.NEXT_OUTPUT === 'export' ? 'export' : undefined,
    // Improve caching with static assets
    async headers() {
        const isDev = process.env.NODE_ENV === 'development'
        
        return [
            {
                source: '/(.*)',
                headers: [
                    {
                        key: 'X-Content-Type-Options',
                        value: 'nosniff',
                    },
                    {
                        key: 'X-Frame-Options',
                        value: 'DENY',
                    },
                    {
                        key: 'X-XSS-Protection',
                        value: '1; mode=block',
                    },
                ],
            },
            // Static assets caching - reduced timing for production
            {
                source: '/(.*)\\.(ico|png|jpg|jpeg|gif|webp|svg)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=3600, must-revalidate', // 1 hour instead of 1 year
                    },
                ],
            },
            // Font caching - reduced timing for production
            {
                source: '/(.*)\\.(woff|woff2|eot|ttf|otf)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=86400, must-revalidate', // 1 day instead of 1 year
                    },
                ],
            },
            // CSS and JS caching - reduced timing for production
            {
                source: '/(.*)\\.(css|js)$',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=1800, must-revalidate', // 30 minutes instead of 1 year
                    },
                    {
                        key: 'ETag',
                        value: `"${Date.now()}"`, // Force cache invalidation
                    },
                ],
            },
            // API routes - no caching
            {
                source: '/api/(.*)',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: 'no-cache, no-store, must-revalidate',
                    },
                ],
            },
            // Dynamic pages - short cache
            {
                source: '/((?!_next|api).)*',
                headers: [
                    {
                        key: 'Cache-Control',
                        value: isDev 
                            ? 'no-cache, no-store, must-revalidate' 
                            : 'public, max-age=300, must-revalidate', // 5 minutes for pages
                    },
                ],
            },
        ]
    },
};

module.exports = nextConfig;
